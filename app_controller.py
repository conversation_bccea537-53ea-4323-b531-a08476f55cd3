import os
import sys
import json
import threading
import subprocess
import base64
from io import BytesIO
from pathlib import Path
from PyQt6.QtCore import QObject, QThread, pyqtSignal, QTimer, QByteArray
from PyQt6.QtGui import QPixmap
from PIL import Image

from api.api_client import TogetherAIClient
from api.runware_client import RunwareAIClient
from config_manager import ConfigManager

from logger import get_logger

class ProcessImageGenerationThread(QThread):
    """Thread for generating images using a separate process to avoid KeyboardInterrupt issues."""

    # Signal emitted when image generation is complete
    image_generated = pyqtSignal(object)
    # Signal emitted when an error occurs
    error_occurred = pyqtSignal(str)
    # Signal emitted when the thread is about to start processing the image
    processing_started = pyqtSignal()
    # Signal emitted to update progress
    progress_update = pyqtSignal(int)  # progress percentage (0-100)

    def __init__(self, api_key, prompt, width, height, steps, seed):
        """Initialize the image generation thread.

        Args:
            api_key (str): API key for the image generation service
            prompt (str): Text prompt for image generation
            width (int): Width of the generated image
            height (int): Height of the generated image
            steps (int): Number of generation steps
            seed (int, optional): Seed for reproducible generation
        """
        super().__init__()
        self.logger = get_logger()
        self.api_key = api_key
        self.prompt = prompt
        self.width = width
        self.height = height
        self.steps = steps
        self.seed = seed
        self.logger.debug(f"ProcessImageGenerationThread initialized with size: {width}x{height}, steps: {steps}, seed: {seed}")

        # Set thread properties to make it more robust
        self.setTerminationEnabled(True)

    def run(self):
        """Run the image generation thread using a direct API call instead of a subprocess."""
        self.logger.debug("Starting image generation process")

        try:
            # Check if we're running as a packaged executable
            is_packaged = getattr(sys, 'frozen', False)
            self.logger.debug(f"Running as packaged executable: {is_packaged}")

            # Emit initial progress
            self.progress_update.emit(10)

            # Use direct API call instead of subprocess
            self.logger.debug(f"Using direct API call for image generation")

            # Create a Together AI client directly
            from api.api_client import TogetherAIClient

            # Generate the image directly
            result_data = self._generate_image_directly()

            # Emit progress update
            self.progress_update.emit(75)

            # Check if the generation was successful
            if result_data["status"] != "success":
                error_message = result_data.get('message', 'Unknown error')
                self.logger.error(f"Image generation failed: {error_message}")

                # Check if it's a rate limit error
                if result_data.get('rate_limited', False) or "rate limit" in error_message.lower():
                    self.logger.warning("Rate limit detected. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.")
                    self.error_occurred.emit("Rate limit exceeded. Please wait a moment before trying again. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.")
                else:
                    self.error_occurred.emit(f"Image generation failed: {error_message}")
                return

            # Signal that we're about to process the image
            self.processing_started.emit()

            # Decode the base64 image data
            image_data = base64.b64decode(result_data["image_data"])

            # Create a PIL Image from the image data and make a copy to ensure it's fully loaded
            image = Image.open(BytesIO(image_data)).copy()

            # Emit the image for display and saving in the main thread
            self.logger.debug(f"Image generated successfully: {image.width}x{image.height}")
            self.image_generated.emit(image)

        except Exception as e:
            self.logger.error(f"Error in image generation thread: {str(e)}", exc_info=True)
            self.error_occurred.emit(f"Error in image generation: {str(e)}")

    def _generate_image_directly(self):
        """Generate an image using the Together AI API directly.

        Returns:
            dict: Result data with status and image_data
        """
        try:
            import requests

            # API endpoint
            url = "https://api.together.xyz/v1/images/generations"

            # Request headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Ensure width and height are multiples of 64 for Together AI
            width = int(self.width)
            height = int(self.height)
            width = ((width + 32) // 64) * 64
            height = ((height + 32) // 64) * 64

            # Ensure dimensions are within the maximum allowed (1408)
            width = min(width, 1408)
            height = min(height, 1408)

            # Using the specific Together AI FLUX.1-schnell-Free model
            model_id = "black-forest-labs/FLUX.1-schnell-Free"

            # Create the payload
            payload = {
                "model": model_id,
                "prompt": self.prompt,
                "width": width,
                "height": height,
                "steps": int(self.steps),
                "response_format": "b64_json"
            }

            if self.seed is not None and self.seed != -1:
                payload["seed"] = int(self.seed)

            self.logger.debug(f"Sending request to {url}")
            response = requests.post(url, headers=headers, json=payload)

            # Check for errors
            if response.status_code != 200:
                self.logger.error(f"API request failed with status code {response.status_code}")
                self.logger.error(f"Response content: {response.text}")

                # Handle rate limit errors (429)
                if response.status_code == 429:
                    try:
                        error_json = response.json()
                        error_message = error_json.get('error', {}).get('message', '')

                        # Check if it's a rate limit error
                        if "rate limit" in error_message.lower():
                            self.logger.warning("Rate limit exceeded. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.")
                            return {
                                "status": "error",
                                "message": "Rate limit exceeded. Please wait a moment before trying again. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.",
                                "rate_limited": True
                            }
                    except:
                        pass

                return {
                    "status": "error",
                    "message": f"API request failed with status code {response.status_code}: {response.text}"
                }

            # Parse the response
            result = response.json()
            self.logger.debug("Successfully received response from API")

            # Get the base64 encoded image
            image_data = result["data"][0]["b64_json"]

            # Return the result
            return {
                "status": "success",
                "image_data": image_data
            }
        except Exception as e:
            self.logger.error(f"Error generating image: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "message": str(e)
            }

    def _create_inline_script(self):
        """Create an inline Python script for image generation.

        This approach avoids issues with importing modules in a separate process.

        Returns:
            str: Python script content
        """
        return '''
import sys
import json
import base64
import requests
from io import BytesIO

def generate_image(api_key, prompt, width, height, steps, seed=None):
    """Generate an image using the Together AI API."""
    try:
        # API endpoint
        url = "https://api.together.xyz/v1/images/generations"

        # Request headers
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

        # Ensure width and height are multiples of 64 for Together AI
        width = int(width)
        height = int(height)
        width = ((width + 32) // 64) * 64
        height = ((height + 32) // 64) * 64

        # Ensure dimensions are within the maximum allowed (1408)
        width = min(width, 1408)
        height = min(height, 1408)

        # Using the specific Together AI FLUX.1-schnell-Free model
        model_id = "black-forest-labs/FLUX.1-schnell-Free"

        # Create the payload
        payload = {
            "model": model_id,
            "prompt": prompt,
            "width": width,
            "height": height,
            "steps": int(steps),
            "response_format": "b64_json"
        }

        if seed is not None and seed != "-1":
            payload["seed"] = int(seed)

        # Print the payload for debugging
        print(f"Payload: {json.dumps(payload)}")

        # Make the API request
        print(f"Sending request to {url}")
        response = requests.post(url, headers=headers, json=payload)

        # Check for errors
        if response.status_code != 200:
            print(f"API request failed with status code {response.status_code}")
            print(f"Response content: {response.text}")

            # Handle rate limit errors (429)
            if response.status_code == 429:
                try:
                    error_json = response.json()
                    error_message = error_json.get('error', {}).get('message', '')

                    # Check if it's a rate limit error
                    if "rate limit" in error_message.lower():
                        print("Rate limit exceeded. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.")
                        return {
                            "status": "error",
                            "message": "Rate limit exceeded. Please wait a moment before trying again. The FLUX.1-schnell-Free model has a limit of 9 queries per minute.",
                            "rate_limited": True
                        }
                except:
                    pass

            return {
                "status": "error",
                "message": f"API request failed with status code {response.status_code}: {response.text}"
            }

        # Parse the response
        result = response.json()
        print("Successfully received response from API")

        # Get the base64 encoded image
        image_data = result["data"][0]["b64_json"]

        # Return the result
        return {
            "status": "success",
            "image_data": image_data
        }
    except Exception as e:
        import traceback
        print(f"Error generating image: {str(e)}")
        traceback.print_exc()
        return {
            "status": "error",
            "message": str(e)
        }

if __name__ == "__main__":
    # Parse command line arguments
    if len(sys.argv) < 6:
        print("Usage: python image_generator.py <api_key> <prompt> <width> <height> <steps> [seed]")
        sys.exit(1)

    api_key = sys.argv[1]
    prompt = sys.argv[2]
    width = sys.argv[3]
    height = sys.argv[4]
    steps = sys.argv[5]
    seed = sys.argv[6] if len(sys.argv) > 6 else None

    # Generate the image
    result = generate_image(api_key, prompt, width, height, steps, seed)

    # Print the result as JSON
    print(json.dumps(result))
'''

class ImageGenerationThread(QThread):
    """Thread for generating images without blocking the UI."""

    # Signal emitted when image generation is complete
    image_generated = pyqtSignal(object)
    # Signal emitted when an error occurs
    error_occurred = pyqtSignal(str)
    # Signal emitted when the thread is about to start processing the image
    processing_started = pyqtSignal()
    # Signal emitted to update progress
    progress_update = pyqtSignal(int)  # progress percentage (0-100)

    def __init__(self, api_client, prompt, width, height, steps, seed, model_id=None):
        """Initialize the image generation thread.

        Args:
            api_client: API client instance (TogetherAIClient or RunwareAIClient)
            prompt (str): Text prompt for image generation
            width (int): Width of the generated image
            height (int): Height of the generated image
            steps (int): Number of generation steps
            seed (int, optional): Seed for reproducible generation
            model_id (str, optional): ID of the model to use for generation
        """
        super().__init__()
        self.logger = get_logger()
        self.api_client = api_client
        self.prompt = prompt
        self.width = width
        self.height = height
        self.steps = steps
        self.seed = seed
        self.model_id = model_id
        self.logger.debug(f"ImageGenerationThread initialized with size: {width}x{height}, steps: {steps}, seed: {seed}, model: {model_id}")

        # Set thread properties to make it more robust
        self.setTerminationEnabled(True)

    def _generate_image_safely(self):
        """Generate the image with robust error handling.

        This method is separated from run() to isolate potential
        KeyboardInterrupt issues and make the code more robust.

        Returns:
            PIL.Image.Image: Generated image as PIL Image object or None if failed
        """
        try:
            # Check if the API client supports model_id parameter
            if hasattr(self.api_client, 'get_available_models') and self.model_id:
                # Generate the image with model_id
                image = self.api_client.generate_image(
                    self.prompt,
                    self.width,
                    self.height,
                    self.steps,
                    self.seed,
                    self.model_id
                )
            else:
                # Generate the image without model_id (for backward compatibility)
                image = self.api_client.generate_image(
                    self.prompt,
                    self.width,
                    self.height,
                    self.steps,
                    self.seed
                )

            # Make a copy to ensure the image is fully loaded
            image_copy = image.copy()
            self.logger.debug(f"Image generated successfully: {image_copy.width}x{image_copy.height}")
            return image_copy
        except KeyboardInterrupt:
            self.logger.error("KeyboardInterrupt detected during image generation", exc_info=True)
            return None
        except Exception as e:
            self.logger.error(f"Error in image generation: {str(e)}", exc_info=True)
            return None

    def run(self):
        """Run the image generation thread."""
        self.logger.debug("Starting image generation thread")

        # Emit initial progress
        self.progress_update.emit(0)

        # Emit progress before generation
        self.progress_update.emit(10)

        # Generate the image using the safe method
        image = self._generate_image_safely()

        # Emit progress after generation
        self.progress_update.emit(75)

        # Check if image generation was successful
        if image is not None:
            # Signal that we're about to process the image
            self.processing_started.emit()

            # Emit progress before sending image
            self.progress_update.emit(90)

            # Emit the image for display and saving in the main thread
            # The image will be processed in the main thread via _on_image_generated
            self.image_generated.emit(image)
        else:
            # Signal that an error occurred
            self.error_occurred.emit("Failed to generate image")

class AppController(QObject):
    """Controller for the application."""

    # Signals
    status_changed = pyqtSignal(str)
    image_generated = pyqtSignal(QPixmap)
    generation_started = pyqtSignal()
    generation_progress = pyqtSignal(int)  # progress percentage (0-100)
    generation_finished = pyqtSignal()
    error_occurred = pyqtSignal(str)

    # Bulk generation signals
    bulk_generation_started = pyqtSignal(int)  # total_prompts
    bulk_generation_progress = pyqtSignal(int, int)  # current, total
    bulk_generation_finished = pyqtSignal(str)  # output_folder
    bulk_prompt_processing = pyqtSignal(str, int, int)  # prompt, index, total

    def __init__(self):
        """Initialize the controller."""
        super().__init__()

        # Initialize logger
        self.logger = get_logger()
        self.logger.info("Initializing AppController")

        # Initialize config manager
        self.config_manager = ConfigManager()
        self.logger.debug("Config manager initialized")

        # Initialize license manager
        from license_manager import LicenseManager
        license_server_url = self.config_manager.get_license_server_url()
        self.license_manager = LicenseManager(self.config_manager, license_server_url)
        self.logger.debug("License manager initialized")

        # Initialize API client
        self.api_client = None
        self._init_api_client()

        # Initialize variables
        self.current_image = None
        self.generation_thread = None

        # Create images directory
        self.project_dir = Path(os.path.dirname(os.path.abspath(sys.argv[0])))
        self.images_dir = self.project_dir / "generated_images"
        os.makedirs(self.images_dir, exist_ok=True)
        self.logger.debug(f"Images directory: {self.images_dir}")

        # Load settings
        self.default_steps = self.config_manager.get_setting("default_steps", 4)
        self.default_image_size = self.config_manager.get_setting("default_image_size", "1024x768")
        self.logger.debug(f"Settings loaded: default_steps={self.default_steps}, default_image_size={self.default_image_size}")

    def _init_api_client(self):
        """Initialize the API client based on the default provider."""
        try:
            # Get the default provider from settings
            provider = self.config_manager.get_setting("default_provider", "together_ai")
            self.logger.debug(f"Initializing API client for provider: {provider}")

            if provider == "together_ai":
                self.api_client = TogetherAIClient(config_manager=self.config_manager)
            elif provider == "runware_ai":
                self.api_client = RunwareAIClient(config_manager=self.config_manager)
            else:
                # Fallback to Together AI if provider is unknown
                self.logger.warning(f"Unknown provider: {provider}, falling back to Together AI")
                self.api_client = TogetherAIClient(config_manager=self.config_manager)

            self.logger.info(f"API client for {provider} initialized successfully")
        except ValueError as e:
            self.logger.error(f"Failed to initialize API client: {str(e)}")
            self.api_client = None

    def update_api_key(self, api_key, provider="together_ai"):
        """Update the API key for the specified provider.

        Args:
            api_key (str): New API key to use.
            provider (str): Provider to update the API key for.
        """
        self.logger.debug(f"Updating API key for {provider}: {'*' * 5}{api_key[-5:] if api_key else 'None'}")

        # Update the current API client if it's for the same provider
        current_provider = self.config_manager.get_setting("default_provider", "together_ai")

        if current_provider == provider:
            if self.api_client:
                self.logger.debug(f"Updating existing {provider} API client")
                self.api_client.update_api_key(api_key)
            else:
                self.logger.debug(f"Creating new {provider} API client with provided key")
                try:
                    if provider == "together_ai":
                        self.api_client = TogetherAIClient(api_key=api_key, config_manager=self.config_manager)
                    elif provider == "runware_ai":
                        self.api_client = RunwareAIClient(api_key=api_key, config_manager=self.config_manager)
                    else:
                        # Fallback to Together AI if provider is unknown
                        self.logger.warning(f"Unknown provider: {provider}, falling back to Together AI")
                        self.api_client = TogetherAIClient(api_key=api_key, config_manager=self.config_manager)

                    self.logger.info(f"API client for {provider} created successfully")
                except ValueError as e:
                    self.logger.error(f"Failed to create {provider} API client: {str(e)}")
                    self.error_occurred.emit(str(e))
        else:
            self.logger.debug(f"Not updating current API client as it's for a different provider ({current_provider} vs {provider})")

    def switch_provider(self, provider):
        """Switch to a different API provider.

        Args:
            provider (str): Provider to switch to.
        """
        self.logger.info(f"Switching API provider to: {provider}")

        # Save the new provider in settings
        self.config_manager.set_setting("default_provider", provider)

        # Force reload the config to ensure we have the latest version
        self.config_manager._load_config()

        # Initialize the new API client
        try:
            if provider == "together_ai":
                api_key = self.config_manager.get_api_key("together_ai")
                self.logger.debug(f"Together AI API key: {'Found' if api_key else 'Not found'}")
                if not api_key:
                    raise ValueError("API key must be provided or set in the config file for Together AI")
                self.api_client = TogetherAIClient(api_key=api_key, config_manager=self.config_manager)
            elif provider == "runware_ai":
                api_key = self.config_manager.get_api_key("runware_ai")
                self.logger.debug(f"Runware AI API key: {'Found' if api_key else 'Not found'}")
                if not api_key:
                    raise ValueError("API key must be provided or set in the config file for Runware AI")
                self.api_client = RunwareAIClient(api_key=api_key, config_manager=self.config_manager)
            else:
                # Fallback to Together AI if provider is unknown
                self.logger.warning(f"Unknown provider: {provider}, falling back to Together AI")
                api_key = self.config_manager.get_api_key("together_ai")
                self.logger.debug(f"Together AI API key: {'Found' if api_key else 'Not found'}")
                if not api_key:
                    raise ValueError("API key must be provided or set in the config file for Together AI")
                self.api_client = TogetherAIClient(api_key=api_key, config_manager=self.config_manager)

            self.logger.info(f"Switched to {provider} API client successfully")
            return True
        except ValueError as e:
            self.logger.error(f"Failed to switch to {provider} API client: {str(e)}")
            self.error_occurred.emit(f"Failed to switch to {provider}: {str(e)}")
            return False

    def get_available_models(self):
        """Get list of available models from the current API provider.

        Returns:
            list: List of available models with id and name.
        """
        if not self.api_client:
            self.logger.error("Cannot get available models: API client not initialized")
            return []

        try:
            if hasattr(self.api_client, 'get_available_models'):
                return self.api_client.get_available_models()
            else:
                self.logger.warning("Current API client does not support listing available models")
                return []
        except Exception as e:
            self.logger.error(f"Error getting available models: {str(e)}")
            return []

    def on_prompt_changed(self, _):
        """Handle prompt text changes.

        Args:
            _ (str): New prompt text (unused).
        """
        # Auto-generate feature has been removed
        pass

    def parse_image_size(self, size_text):
        """Parse image size text into width and height.

        Args:
            size_text (str): Size text in format "width x height".

        Returns:
            tuple: (width, height) as integers.
        """
        width, height = map(int, size_text.split(" x "))
        return width, height

    def adjust_dimensions_for_together_ai(self, width, height):
        """Adjust dimensions to be multiples of 64 for Together AI.

        Args:
            width (int): Original width
            height (int): Original height

        Returns:
            tuple: (adjusted_width, adjusted_height) as integers
        """
        # Round to nearest multiple of 64
        adjusted_width = ((width + 32) // 64) * 64
        adjusted_height = ((height + 32) // 64) * 64

        # Ensure dimensions are within the maximum allowed (1408)
        adjusted_width = min(adjusted_width, 1408)
        adjusted_height = min(adjusted_height, 1408)

        # Log if adjustments were made
        if adjusted_width != width or adjusted_height != height:
            self.logger.debug(f"Adjusted dimensions from {width}x{height} to {adjusted_width}x{adjusted_height} for Together AI")

        return adjusted_width, adjusted_height

    def generate_image(self, prompt=None, size_text=None, steps=None, seed=None, model_id=None):
        """Generate an image based on the given parameters.

        Args:
            prompt (str, optional): Text prompt for image generation.
            size_text (str, optional): Size text in format "width x height".
            steps (int, optional): Number of generation steps.
            seed (int, optional): Seed for reproducible generation.
            model_id (str, optional): ID of the model to use for generation.
        """
        self.logger.info(f"Generate image request: prompt='{prompt}', size={size_text}, steps={steps}, seed={seed}, model={model_id}")

        # Check if API client is available
        if not self.api_client:
            self.logger.error("Cannot generate image: API client not initialized")
            self.error_occurred.emit("API key not configured")
            return

        # Check if prompt is provided
        if not prompt or not prompt.strip():
            self.logger.warning("Cannot generate image: Empty prompt")
            self.error_occurred.emit("Prompt is required")
            return

        # Check if a generation is already in progress
        if self.generation_thread and self.generation_thread.isRunning():
            self.logger.warning("Image generation already in progress")
            self.error_occurred.emit("Image generation already in progress")
            return

        # Get the current provider first (needed for license checks)
        provider = self.config_manager.get_setting("default_provider", "together_ai")

        # Note: Daily limit checks are now handled in the UI layer (MainWindow)
        # before this method is called to ensure real-time feedback

        # Check if provider is allowed
        if provider == "runware_ai" and not self.license_manager.is_feature_allowed('all_providers'):
            self.logger.warning("Runware AI not allowed on free plan")
            self.error_occurred.emit("Runware AI is only available with Pro plans. Please upgrade or use Together AI.")
            return

        # Parse image size
        if size_text:
            width, height = self.parse_image_size(size_text)
        else:
            width, height = self.parse_image_size(self.default_image_size)

        # Use default steps if not provided
        if steps is None:
            steps = self.default_steps

        # Adjust dimensions for Together AI to be multiples of 16
        if provider == "together_ai":
            width, height = self.adjust_dimensions_for_together_ai(width, height)

        self.logger.debug(f"Image generation parameters: size={width}x{height}, steps={steps}, seed={seed}, model={model_id}")

        # Update status
        self.status_changed.emit("Generating image...")
        self.generation_started.emit()

        try:
            # For Together AI, use the direct API client approach (same as Runware AI)
            if provider == "together_ai":
                # Get the API key
                api_key = self.config_manager.get_api_key("together_ai")
                if not api_key:
                    self.logger.error("Cannot generate image: Together AI API key not found")
                    self.error_occurred.emit("Together AI API key not configured")
                    self.generation_finished.emit()
                    return

                # Create and start generation thread using the API client
                self.logger.debug("Creating image generation thread for Together AI")
                # For Together AI, model_id is not needed (only has one free model)
                self.generation_thread = ImageGenerationThread(
                    self.api_client, prompt, width, height, steps, seed, None
                )

                # Connect signals - ensure we disconnect any previous connections first
                self.logger.debug("Connecting thread signals")
                try:
                    # Disconnect any existing connections to avoid multiple signal handlers
                    self.generation_thread.image_generated.disconnect()
                    self.generation_thread.error_occurred.disconnect()
                    self.generation_thread.processing_started.disconnect()
                    self.generation_thread.finished.disconnect()
                except TypeError:
                    # No connections exist yet, which is fine
                    pass

                # Connect new signal handlers
                self.generation_thread.image_generated.connect(self._on_image_generated)
                self.generation_thread.error_occurred.connect(self._on_generation_error)
                self.generation_thread.processing_started.connect(
                    lambda: self.logger.debug("Image processing started")
                )
                self.generation_thread.progress_update.connect(self.generation_progress.emit)
                self.generation_thread.finished.connect(
                    lambda: self.generation_finished.emit()
                )

                # Start the thread
                self.logger.debug("Starting image generation thread")
                self.generation_thread.start()

            # For Runware AI, use the direct API client approach
            elif provider == "runware_ai":
                # Create and start generation thread using the API client
                self.logger.debug("Creating image generation thread for Runware AI")
                self.generation_thread = ImageGenerationThread(
                    self.api_client, prompt, width, height, steps, seed, model_id
                )

                # Connect signals - ensure we disconnect any previous connections first
                self.logger.debug("Connecting thread signals")
                try:
                    # Disconnect any existing connections to avoid multiple signal handlers
                    self.generation_thread.image_generated.disconnect()
                    self.generation_thread.error_occurred.disconnect()
                    self.generation_thread.processing_started.disconnect()
                    self.generation_thread.finished.disconnect()
                except TypeError:
                    # No connections exist yet, which is fine
                    pass

                # Connect new signal handlers
                self.generation_thread.image_generated.connect(self._on_image_generated)
                self.generation_thread.error_occurred.connect(self._on_generation_error)
                self.generation_thread.processing_started.connect(
                    lambda: self.logger.debug("Image processing started")
                )
                # Add progress update connection if available
                if hasattr(self.generation_thread, 'progress_update'):
                    self.generation_thread.progress_update.connect(self.generation_progress.emit)
                self.generation_thread.finished.connect(
                    lambda: self.generation_finished.emit()
                )

                # Start the thread
                self.logger.debug("Starting image generation thread")
                self.generation_thread.start()

            else:
                self.logger.error(f"Unknown provider: {provider}")
                self.error_occurred.emit(f"Unknown provider: {provider}")
                self.generation_finished.emit()

        except Exception as e:
            self.logger.error(f"Failed to start image generation thread: {str(e)}", exc_info=True)
            self.error_occurred.emit(f"Failed to start image generation: {str(e)}")
            self.generation_finished.emit()

    def _on_image_generated(self, image):
        """Handle generated image.

        Args:
            image: PIL Image object.
        """
        try:
            self.logger.info("Image generation complete, processing result")

            # Store the image (make a copy to ensure it's fully loaded)
            self.current_image = image.copy()

            # Save the image to disk first (in the background thread)
            import time
            timestamp = int(time.time())
            file_path = os.path.join(self.images_dir, f"image_{timestamp}.png")

            # Ensure the directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Save the image
            self.current_image.save(file_path)
            self.logger.info(f"Image saved to: {file_path}")

            # Note: Usage tracking is now handled in the UI layer (MainWindow)
            # to ensure real-time updates and proper signal handling

            # Convert to bytes for safe thread transfer
            img_byte_arr = BytesIO()
            self.current_image.save(img_byte_arr, format='PNG')
            img_bytes = img_byte_arr.getvalue()

            # Use QTimer to process the image in the main thread
            # This ensures all UI operations happen in the main thread

            def process_in_main_thread():
                try:
                    # Convert bytes back to image in the main thread
                    pixmap = QPixmap()
                    pixmap.loadFromData(QByteArray(img_bytes))

                    # Update UI in the main thread
                    self.image_generated.emit(pixmap)
                    self.status_changed.emit(f"Image generated and saved to {file_path}")

                    # Final progress update
                    self.generation_progress.emit(100)

                    # Signal that generation is complete
                    self.generation_finished.emit()
                except Exception as e:
                    self.logger.error(f"Error in main thread processing: {str(e)}", exc_info=True)
                    self.error_occurred.emit(f"Error processing image: {str(e)}")
                    self.generation_finished.emit()

            # Schedule the UI update for the next event loop iteration in the main thread
            QTimer.singleShot(0, process_in_main_thread)

        except Exception as e:
            self.logger.error(f"Error in image generation callback: {str(e)}", exc_info=True)
            self.error_occurred.emit(f"Error processing image: {str(e)}")
            self.generation_finished.emit()

    def _save_image_to_folder(self, image):
        """Save the image to the generated_images folder.

        Args:
            image: PIL Image object
        """
        try:
            # Generate a filename based on timestamp
            import time
            timestamp = int(time.time())
            file_path = os.path.join(self.images_dir, f"image_{timestamp}.png")
            self.logger.debug(f"Saving image to: {file_path}")

            # Ensure the directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Save the image
            image.save(file_path)
            self.logger.info(f"Image saved to: {file_path}")
            self.status_changed.emit(f"Image saved to {file_path}")
        except KeyboardInterrupt:
            self.logger.error("KeyboardInterrupt detected during image saving", exc_info=True)
            self.error_occurred.emit("Operation interrupted by user")
            self.status_changed.emit("Save operation interrupted")
            # Don't re-raise the KeyboardInterrupt to prevent app closure
        except Exception as e:
            self.logger.error(f"Failed to save image: {str(e)}")
            self.error_occurred.emit(f"Failed to save image: {str(e)}")

    def _on_generation_error(self, error_message):
        """Handle image generation error.

        Args:
            error_message (str): Error message.
        """
        try:
            self.logger.error(f"Image generation failed: {error_message}")
            self.error_occurred.emit(error_message)
            self.status_changed.emit(f"Error: {error_message}")

            # Reset progress on error
            self.generation_progress.emit(0)
        except Exception as e:
            self.logger.error(f"Error handling generation error: {str(e)}", exc_info=True)
        finally:
            # Always signal that generation is complete
            self.generation_finished.emit()

    def save_image(self, file_path=None):
        """Save the current image to disk.

        Args:
            file_path (str, optional): Path to save the image to. If not provided,
                                      a filename will be generated in the images directory.

        Returns:
            str: Path to the saved file if successful, None otherwise.
        """
        if not self.current_image:
            self.logger.warning("Cannot save image: No image available")
            self.error_occurred.emit("No image to save")
            return None

        if not file_path:
            # Generate a filename based on timestamp
            import time
            timestamp = int(time.time())
            file_path = os.path.join(self.images_dir, f"image_{timestamp}.png")
            self.logger.debug(f"No file path provided, using generated path: {file_path}")

        self.logger.debug(f"Saving image to: {file_path}")

        # Create a copy of the image to avoid any issues
        image_copy = self.current_image.copy()
        final_path = file_path  # Store for closure

        # Use QTimer to ensure UI updates happen in the main thread

        # Define a function to save in a background thread
        def save_in_background():
            try:
                # Ensure the directory exists
                os.makedirs(os.path.dirname(final_path), exist_ok=True)

                # Save the image (file I/O operation)
                image_copy.save(final_path)
                self.logger.info(f"Image saved successfully to: {final_path}")

                # Use QTimer to update UI in the main thread
                QTimer.singleShot(0, lambda: self.status_changed.emit(f"Image saved to {final_path}"))
            except Exception as e:
                self.logger.error(f"Failed to save image: {str(e)}")
                # Use QTimer to update UI in the main thread
                QTimer.singleShot(0, lambda: self.error_occurred.emit(f"Failed to save image: {str(e)}"))
                QTimer.singleShot(0, lambda: self.status_changed.emit("Error saving image"))

        # Start a new thread to handle saving to avoid UI blocking
        threading.Thread(target=save_in_background, daemon=True).start()

        # Return the path immediately - the actual saving happens in the background
        return file_path

    # Auto-generate feature has been removed

    def set_default_steps(self, steps):
        """Set default steps setting.

        Args:
            steps (int): Default number of steps.
        """
        self.default_steps = steps
        self.config_manager.set_setting("default_steps", steps)

    def set_default_image_size(self, size_text):
        """Set default image size setting.

        Args:
            size_text (str): Size text in format "width x height".
        """
        self.default_image_size = size_text
        self.config_manager.set_setting("default_image_size", size_text)

    def start_bulk_generation(self, prompts_file, size_text, steps, output_folder=None, model_id=None):
        """Start bulk image generation from a file of prompts.

        Args:
            prompts_file (str): Path to text file with prompts (one per line).
            size_text (str): Size text in format "width x height".
            steps (int): Number of generation steps.
            output_folder (str, optional): Custom output folder. If None, uses default.
            model_id (str, optional): ID of the model to use for generation.
        """
        self.logger.info(f"Starting bulk generation from file: {prompts_file}, model: {model_id}")

        # Create a thread to handle the bulk generation
        thread = threading.Thread(
            target=self._bulk_generation_thread,
            args=(prompts_file, size_text, steps, output_folder, model_id),
            daemon=True
        )
        thread.start()

    def _bulk_generation_thread(self, prompts_file, size_text, steps, output_folder=None, model_id=None):
        """Thread function for bulk image generation.

        Args:
            prompts_file (str): Path to text file with prompts (one per line).
            size_text (str): Size text in format "width x height".
            steps (int): Number of generation steps.
            output_folder (str, optional): Custom output folder. If None, uses default.
            model_id (str, optional): ID of the model to use for generation.
        """
        try:
            # Read prompts from file
            with open(prompts_file, 'r', encoding='utf-8') as f:
                prompts = [line.strip() for line in f if line.strip()]

            if not prompts:
                self.logger.warning("No valid prompts found in file")
                QTimer.singleShot(0, lambda: self.error_occurred.emit("No valid prompts found in file"))
                return

            # Create output folder with timestamp
            import time
            timestamp = int(time.time())

            if output_folder:
                bulk_folder = output_folder
            else:
                bulk_folder = os.path.join(self.images_dir, f"bulk_{timestamp}")

            os.makedirs(bulk_folder, exist_ok=True)
            self.logger.info(f"Created bulk output folder: {bulk_folder}")

            # Parse image size
            width, height = self.parse_image_size(size_text)

            # Get the current provider
            provider = self.config_manager.get_setting("default_provider", "together_ai")

            # Adjust dimensions for Together AI to be multiples of 16
            if provider == "together_ai":
                width, height = self.adjust_dimensions_for_together_ai(width, height)

            # Signal that bulk generation has started - direct call for immediate update
            total_prompts = len(prompts)
            self.bulk_generation_started.emit(total_prompts)

            # Force the main thread to process events
            QTimer.singleShot(0, lambda: None)

            # Process each prompt
            for i, prompt in enumerate(prompts):
                # Signal which prompt we're processing - direct call for immediate update
                self.bulk_prompt_processing.emit(prompt, i, total_prompts)

                # Force the main thread to process events
                QTimer.singleShot(0, lambda: None)

                # Skip empty prompts
                if not prompt:
                    self.logger.warning(f"Skipping empty prompt at line {i+1}")
                    continue

                try:
                    # Generate image for this prompt
                    self.logger.info(f"Generating image {i+1}/{total_prompts} for prompt: {prompt}")

                    # Get the current provider
                    provider = self.config_manager.get_setting("default_provider", "together_ai")

                    # Get the API key for the current provider
                    if provider == "together_ai":
                        api_key = self.config_manager.get_api_key("together_ai")
                        if not api_key:
                            self.logger.error("Cannot generate image: Together AI API key not found")
                            QTimer.singleShot(0, lambda: self.error_occurred.emit("Together AI API key not configured"))
                            break
                    else:
                        api_key = self.config_manager.get_api_key("runware_ai")
                        if not api_key:
                            self.logger.error("Cannot generate image: Runware AI API key not found")
                            QTimer.singleShot(0, lambda: self.error_occurred.emit("Runware AI API key not configured"))
                            break

                    # Use a more direct approach instead of signals
                    # This avoids potential issues with signal connections across threads
                    self.logger.debug(f"Directly generating image for prompt: {prompt}")

                    # Add retry logic for rate limit errors
                    max_retries = 3
                    retry_count = 0
                    retry_delay = 6  # Start with 6 seconds (for 10 images/minute rate limit)

                    while retry_count <= max_retries:
                        try:
                            # Get the current provider
                            provider = self.config_manager.get_setting("default_provider", "together_ai")

                            # Create the appropriate API client based on provider
                            if provider == "together_ai":
                                # Generate the image directly using the Together AI client
                                api_client = TogetherAIClient(api_key=api_key)

                                # Make the API request
                                self.logger.debug(f"Making direct API request to Together AI for prompt: {prompt} (Attempt {retry_count+1}/{max_retries+1})")
                                image = api_client.generate_image(prompt, width, height, steps, None)  # Together AI only has one free model
                            else:
                                # Generate the image directly using the Runware AI client
                                api_client = RunwareAIClient(api_key=api_key)

                                # Make the API request
                                self.logger.debug(f"Making direct API request to Runware AI for prompt: {prompt}, model: {model_id} (Attempt {retry_count+1}/{max_retries+1})")
                                image = api_client.generate_image(prompt, width, height, steps, None, model_id)  # Runware AI uses model_id

                            if image is None:
                                self.logger.error(f"Failed to generate image for prompt: {prompt}")
                                break

                            self.logger.debug(f"Successfully generated image for prompt: {prompt}")
                            break  # Success, exit the retry loop

                        except Exception as e:
                            error_msg = str(e)
                            self.logger.error(f"Error in direct image generation: {error_msg}", exc_info=True)

                            # Check if it's a rate limit error
                            if "rate limit" in error_msg.lower() and retry_count < max_retries:
                                retry_count += 1
                                # Exponential backoff for retries
                                wait_time = retry_delay * (2 ** (retry_count - 1))
                                self.logger.warning(f"Rate limit hit. Retrying in {wait_time} seconds... (Attempt {retry_count+1}/{max_retries+1})")

                                # Update UI with retry status
                                QTimer.singleShot(0, lambda r=retry_count, m=max_retries, w=wait_time:
                                                self.status_changed.emit(f"Rate limit hit. Retrying prompt in {w}s... (Attempt {r+1}/{m+1})"))

                                # Wait before retrying
                                import time
                                time.sleep(wait_time)
                            else:
                                # Not a rate limit error or max retries reached
                                # Update UI with error
                                QTimer.singleShot(0, lambda msg=error_msg:
                                                self.error_occurred.emit(f"Error generating image: {msg}"))
                                break  # Exit the retry loop

                    # If we've exhausted all retries and still failed, skip this prompt
                    if retry_count > max_retries or image is None:
                        self.logger.error(f"Failed to generate image after {max_retries+1} attempts, skipping prompt: {prompt}")
                        continue

                    # Save the image with timestamp and index
                    filename = f"image_{timestamp}_{i+1:03d}.png"
                    file_path = os.path.join(bulk_folder, filename)

                    # Save the image
                    image.save(file_path)
                    self.logger.info(f"Saved image {i+1}/{total_prompts} to: {file_path}")

                    # Signal progress - use i+1 to show correct progress (1-based for display)
                    # Use a direct call to ensure immediate update
                    self.bulk_generation_progress.emit(i+1, total_prompts)

                    # Force the main thread to process events
                    QTimer.singleShot(0, lambda: None)

                    # Add a delay to respect API rate limits for the free model
                    # Together AI FLUX.1-schnell-Free model has a limit of 6 images per minute
                    import time

                    # Calculate wait time based on provider
                    if provider == "together_ai":
                        wait_time = 7  # Wait 7 seconds between requests (9 per minute)
                        self.logger.debug(f"Waiting {wait_time} seconds to respect Together AI rate limits (9 images/minute)...")
                    else:
                        wait_time = 6  # Default wait time for other providers
                        self.logger.debug(f"Waiting {wait_time} seconds between requests...")

                    time.sleep(wait_time)

                except Exception as e:
                    self.logger.error(f"Error processing prompt {i+1}: {str(e)}", exc_info=True)
                    continue

            # Signal that bulk generation is complete
            # Use a direct call to ensure the UI updates
            self.logger.info(f"Bulk generation complete. {total_prompts} images saved to {bulk_folder}")

            # Direct call for immediate update
            self.bulk_generation_finished.emit(bulk_folder)
            self.status_changed.emit(f"Bulk generation complete. {total_prompts} images saved.")

            # Force the main thread to process events
            QTimer.singleShot(0, lambda: None)

        except Exception as e:
            self.logger.error(f"Error in bulk generation: {str(e)}", exc_info=True)
            # Direct calls for immediate update
            self.error_occurred.emit(f"Bulk generation error: {str(e)}")
            self.bulk_generation_finished.emit("")

            # Force the main thread to process events
            QTimer.singleShot(0, lambda: None)
