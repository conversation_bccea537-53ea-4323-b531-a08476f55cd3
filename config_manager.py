import os
import json
import sys
from pathlib import Path
from logger import get_logger
from secure_storage import SecureStorage

class ConfigManager:
    """Manages application configuration including API keys."""

    def __init__(self, config_file="config.json"):
        """Initialize the config manager.

        Args:
            config_file (str): Path to the config file, relative to the app directory.
        """
        self.logger = get_logger()

        # Check for config in project directory first
        project_dir = Path(os.path.dirname(os.path.abspath(sys.argv[0])))
        project_config_path = project_dir / config_file

        # Default location in user's home directory
        self.app_dir = Path.home() / ".bulkai"
        home_config_path = self.app_dir / config_file

        # Use project config if it exists, otherwise use home config
        if project_config_path.exists():
            self.config_path = project_config_path
            self.logger.info(f"Using config file from project directory: {self.config_path}")
        else:
            self.config_path = home_config_path
            self.logger.info(f"Using config file from home directory: {self.config_path}")

        # Initialize secure storage for API keys
        self.secure_storage = SecureStorage("BulkAI")

        self.config = self._load_config()

    def _ensure_app_dir(self):
        """Ensure the application directory exists."""
        os.makedirs(self.app_dir, exist_ok=True)

    def _load_config(self):
        """Load configuration from file or create default if it doesn't exist."""
        # Create default config structure
        # Note: API keys are no longer stored in config.json for security
        default_config = {
            "settings": {
                "theme": "dark",
                "default_image_size": "1024x768",
                "default_steps": 28,
                "auto_generate": True,
                "default_provider": "together_ai"
            },
            "licensing": {
                "license_key": "",
                "plan_type": "free",  # free, monthly, yearly, lifetime
                "activation_date": "",
                "expiry_date": "",
                "images_used_today": 0,
                "last_reset_date": "",
                "device_id": "",
                "license_server_url": "https://bulkimages.azanx.com/licensing/api",
                "is_active": False,
                "daily_limit": 10
            },
            "usage_limits": {
                "trial": {
                    "daily_limit": 100,
                    "duration_days": 7,
                    "watermark": True,
                    "bulk_limit": 10
                },
                "monthly": {
                    "daily_limit": -1,  # unlimited
                    "watermark": False,
                    "bulk_limit": -1  # unlimited
                },
                "yearly": {
                    "daily_limit": -1,  # unlimited
                    "watermark": False,
                    "bulk_limit": -1  # unlimited
                },
                "lifetime": {
                    "daily_limit": -1,  # unlimited
                    "watermark": False,
                    "bulk_limit": -1  # unlimited
                }
            },
            "runware_models": [
                {
                    "id": "air://civitai/4201/130090",
                    "name": "Realistic Vision V6.0"
                },
                {
                    "id": "air://civitai/101055/128078",
                    "name": "Dreamshaper XL"
                },
                {
                    "id": "air://civitai/124421/139565",
                    "name": "Juggernaut XL"
                }
            ]
        }

        # If using home directory config, ensure it exists
        if str(self.config_path).startswith(str(self.app_dir)):
            self._ensure_app_dir()

        if not self.config_path.exists():
            self.logger.warning(f"Config file not found at {self.config_path}, creating default")
            self._save_config(default_config)
            return default_config

        try:
            self.logger.debug(f"Loading config from {self.config_path}")
            with open(self.config_path, 'r') as f:
                config = json.load(f)
                self.logger.debug(f"Config loaded successfully: {len(config)} sections")
                return config
        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"Error loading config file: {str(e)}")
            self.logger.warning("Creating new default config file")
            self._save_config(default_config)
            return default_config

    def _save_config(self, config=None):
        """Save configuration to file.

        Args:
            config (dict, optional): Configuration to save. If not provided,
                                    saves the current config.
        """
        if config is None:
            config = self.config

        # If saving to home directory, ensure it exists
        if str(self.config_path).startswith(str(self.app_dir)):
            self._ensure_app_dir()

        try:
            self.logger.debug(f"Saving config to {self.config_path}")

            # Remove api_keys section from config if it exists (for security)
            if "api_keys" in config:
                self.logger.info("Removing API keys from config.json for security")
                del config["api_keys"]

            # Save the config
            with open(self.config_path, 'w') as f:
                json.dump(config, f, indent=4)

            # Verify the config was saved correctly
            self.logger.debug("Config saved, verifying...")
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    saved_config = json.load(f)
                    if "api_keys" in saved_config and "runware_ai" in saved_config["api_keys"]:
                        self.logger.debug("Config verified successfully")
                    else:
                        self.logger.error("Config verification failed: missing expected keys")
            else:
                self.logger.error("Config verification failed: file does not exist")

            self.logger.debug("Config saved successfully")
        except IOError as e:
            self.logger.error(f"Error saving config: {e}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing saved config: {e}")

    def get_api_key(self, service="together_ai"):
        """Get API key for the specified service from secure storage.

        Args:
            service (str): Service name to get API key for.

        Returns:
            str: API key for the service or empty string if not found.
        """
        try:
            api_key = self.secure_storage.get_api_key(service)
            if api_key:
                self.logger.debug(f"Retrieved API key for {service} from secure storage")
                return api_key
            else:
                self.logger.debug(f"No API key found for {service} in secure storage")

                # Check if there's a legacy key in config.json and migrate it
                legacy_key = self._get_legacy_api_key(service)
                if legacy_key:
                    self.logger.info(f"Migrating legacy API key for {service} to secure storage")
                    if self.secure_storage.store_api_key(service, legacy_key):
                        self.logger.info(f"Successfully migrated API key for {service}")
                        return legacy_key
                    else:
                        self.logger.error(f"Failed to migrate API key for {service}")

                return ""
        except Exception as e:
            self.logger.error(f"Error retrieving API key for {service}: {e}")
            return ""

    def set_api_key(self, api_key, service="together_ai"):
        """Set API key for the specified service in secure storage.

        Args:
            api_key (str): API key to set.
            service (str): Service name to set API key for.
        """
        self.logger.debug(f"Setting API key for {service} in secure storage")

        try:
            success = self.secure_storage.store_api_key(service, api_key)
            if success:
                self.logger.debug(f"API key for {service} saved successfully in secure storage")
                return True
            else:
                self.logger.error(f"Failed to save API key for {service} in secure storage")
                return False
        except Exception as e:
            self.logger.error(f"Error saving API key for {service}: {e}")
            return False

    def delete_api_key(self, service="together_ai"):
        """Delete API key for the specified service from secure storage.

        Args:
            service (str): Service name to delete API key for.

        Returns:
            bool: True if successful, False otherwise.
        """
        self.logger.debug(f"Deleting API key for {service} from secure storage")

        try:
            success = self.secure_storage.delete_api_key(service)
            if success:
                self.logger.debug(f"API key for {service} deleted successfully from secure storage")
                return True
            else:
                self.logger.error(f"Failed to delete API key for {service} from secure storage")
                return False
        except Exception as e:
            self.logger.error(f"Error deleting API key for {service}: {e}")
            return False

    def list_stored_api_keys(self):
        """List all services with stored API keys.

        Returns:
            List[str]: List of service names with stored API keys.
        """
        try:
            return self.secure_storage.list_stored_services()
        except Exception as e:
            self.logger.error(f"Error listing stored API keys: {e}")
            return []

    def _get_legacy_api_key(self, service):
        """Get API key from legacy config.json format for migration.

        Args:
            service (str): Service name to get API key for.

        Returns:
            str: API key if found in legacy format, empty string otherwise.
        """
        try:
            # Reload config to check for legacy keys
            with open(self.config_path, 'r') as f:
                config = json.load(f)

            api_keys = config.get("api_keys", {})
            return api_keys.get(service, "")
        except Exception as e:
            self.logger.debug(f"No legacy API key found for {service}: {e}")
            return ""

    def get_license_key(self):
        """Get the current license key."""
        return self.get_setting("license_key", "", section="licensing")

    def set_license_key(self, license_key):
        """Set the license key."""
        self.set_setting("license_key", license_key, section="licensing")

    def get_license_server_url(self):
        """Get the license server URL."""
        return self.get_setting("license_server_url", "https://bulkimages.azanx.com/licensing/api", section="licensing")

    def set_license_server_url(self, url):
        """Set the license server URL."""
        self.set_setting("license_server_url", url, section="licensing")

    def get_device_id(self):
        """Get the device ID."""
        return self.get_setting("device_id", "", section="licensing")

    def set_device_id(self, device_id):
        """Set the device ID."""
        self.set_setting("device_id", device_id, section="licensing")

    def get_plan_type(self):
        """Get the current plan type."""
        return self.get_setting("plan_type", "free", section="licensing")

    def set_plan_type(self, plan_type):
        """Set the plan type."""
        self.set_setting("plan_type", plan_type, section="licensing")

    def get_daily_limit(self):
        """Get the daily image limit."""
        return self.get_setting("daily_limit", 10, section="licensing")

    def set_daily_limit(self, limit):
        """Set the daily image limit."""
        self.set_setting("daily_limit", limit, section="licensing")

    def get_images_used_today(self):
        """Get the number of images used today."""
        return self.get_setting("images_used_today", 0, section="licensing")

    def set_images_used_today(self, count):
        """Set the number of images used today."""
        self.set_setting("images_used_today", count, section="licensing")

    def increment_images_used_today(self):
        """Increment the daily image usage count."""
        current = self.get_images_used_today()
        self.set_images_used_today(current + 1)

    def reset_daily_usage_if_needed(self):
        """Reset daily usage if it's a new day."""
        from datetime import date

        last_reset = self.get_setting("last_reset_date", "", section="licensing")
        today = str(date.today())

        if last_reset != today:
            self.set_images_used_today(0)
            self.set_setting("last_reset_date", today, section="licensing")
            self.logger.debug("Daily usage reset for new day")

    def get_setting(self, key, default=None, section="settings"):
        """Get a setting value.

        Args:
            key (str): Setting key to get.
            default: Default value to return if setting is not found.
            section (str): Section to get the setting from.

        Returns:
            Setting value or default if not found.
        """
        return self.config.get(section, {}).get(key, default)

    def set_setting(self, key, value, section="settings"):
        """Set a setting value.

        Args:
            key (str): Setting key to set.
            value: Value to set for the setting.
            section (str): Section to set the setting in.
        """
        if section not in self.config:
            self.config[section] = {}

        self.config[section][key] = value
        self._save_config()

    def get_all_settings(self):
        """Get all settings.

        Returns:
            dict: All settings.
        """
        return self.config.get("settings", {})

    # Subscription management methods
    def get_subscription_info(self):
        """Get subscription information.

        Returns:
            dict: Subscription information.
        """
        return self.config.get("subscription", {})

    def set_subscription_info(self, key, value):
        """Set subscription information.

        Args:
            key (str): Subscription key to set.
            value: Value to set for the subscription key.
        """
        if "subscription" not in self.config:
            self.config["subscription"] = {}

        self.config["subscription"][key] = value
        self._save_config()

    def get_usage_limits(self, plan_type=None):
        """Get usage limits for a specific plan or current plan.

        Args:
            plan_type (str, optional): Plan type to get limits for.
                                     If None, uses current plan.

        Returns:
            dict: Usage limits for the plan.
        """
        if plan_type is None:
            plan_type = self.config.get("subscription", {}).get("plan_type", "trial")

        return self.config.get("usage_limits", {}).get(plan_type, {})

    def increment_images_used_today(self):
        """Increment daily image usage counter (legacy method for backward compatibility)."""
        import datetime

        today = datetime.date.today().isoformat()
        subscription = self.config.get("subscription", {})

        # Reset daily counter if it's a new day
        if subscription.get("last_reset_date") != today:
            subscription["images_used_today"] = 0
            subscription["last_reset_date"] = today

        # Increment counters
        subscription["images_used_today"] = subscription.get("images_used_today", 0) + 1

        self.config["subscription"] = subscription
        self._save_config()

    def can_generate_image(self):
        """Check if user can generate an image based on their plan limits.

        Returns:
            tuple: (can_generate: bool, reason: str)
        """
        subscription = self.get_subscription_info()
        plan_type = subscription.get("plan_type", "trial")
        limits = self.get_usage_limits(plan_type)

        # Check if subscription is active for paid plans
        if plan_type != "trial" and not subscription.get("is_active", False):
            return False, "Subscription is not active"

        # Check trial expiry
        if plan_type == "trial":
            if subscription.get("trial_started", False):
                import datetime
                trial_start = datetime.datetime.fromisoformat(subscription.get("trial_start_date", ""))
                days_elapsed = (datetime.datetime.now() - trial_start).days
                if days_elapsed >= limits.get("duration_days", 7):
                    return False, "Trial period has expired"

            # Check daily limits for trial only
            daily_limit = limits.get("daily_limit", 100)
            used_today = subscription.get("images_used_today", 0)
            if used_today >= daily_limit:
                return False, f"Daily limit of {daily_limit} images reached"

        # Paid plans have unlimited generation
        return True, "OK"

    def set_subscription_info(self, key, value):
        """Set subscription information.

        Args:
            key (str): Subscription key to set.
            value: Value to set for the subscription key.
        """
        if "subscription" not in self.config:
            self.config["subscription"] = {}

        self.config["subscription"][key] = value
        self._save_config()

    def get_usage_limits(self, plan_type=None):
        """Get usage limits for a specific plan or current plan.

        Args:
            plan_type (str, optional): Plan type to get limits for.
                                     If None, uses current plan.

        Returns:
            dict: Usage limits for the plan.
        """
        if plan_type is None:
            plan_type = self.config.get("subscription", {}).get("plan_type", "trial")

        return self.config.get("usage_limits", {}).get(plan_type, {})

    def increment_image_usage(self):
        """Increment image usage counters (both daily and monthly)."""
        import datetime

        today = datetime.date.today().isoformat()
        subscription = self.config.get("subscription", {})

        # Reset daily counter if it's a new day
        if subscription.get("last_reset_date") != today:
            subscription["images_used_today"] = 0
            subscription["last_reset_date"] = today

        # Increment counters
        subscription["images_used_today"] = subscription.get("images_used_today", 0) + 1
        subscription["images_used_month"] = subscription.get("images_used_month", 0) + 1

        self.config["subscription"] = subscription
        self._save_config()

    def reset_monthly_usage(self):
        """Reset monthly usage counter."""
        subscription = self.config.get("subscription", {})
        subscription["images_used_month"] = 0
        self.config["subscription"] = subscription
        self._save_config()

    def can_generate_image(self):
        """Check if user can generate an image based on their plan limits.

        Returns:
            tuple: (can_generate: bool, reason: str)
        """
        subscription = self.get_subscription_info()
        plan_type = subscription.get("plan_type", "trial")
        limits = self.get_usage_limits(plan_type)

        # Check if subscription is active
        if not subscription.get("is_active", False) and plan_type != "trial":
            return False, "Subscription is not active"

        # Check trial expiry
        if plan_type == "trial":
            if subscription.get("trial_started", False):
                import datetime
                trial_start = datetime.datetime.fromisoformat(subscription.get("trial_start_date", ""))
                days_elapsed = (datetime.datetime.now() - trial_start).days
                if days_elapsed >= limits.get("duration_days", 7):
                    return False, "Trial period has expired"

        # Check daily limits for trial only
        if plan_type == "trial":
            daily_limit = limits.get("daily_limit", 100)
            used_today = subscription.get("images_used_today", 0)
            if used_today >= daily_limit:
                return False, f"Daily limit of {daily_limit} images reached"

        # Lifetime has no limits
        return True, "OK"

    def get_runware_models(self):
        """Get the list of Runware AI models from the configuration.

        Returns:
            list: List of Runware AI models with id and name.
        """
        # Reload the config from disk to ensure we have the latest version
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                self.logger.debug(f"Reloaded config from {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error reloading config: {e}")

        # Get the models
        models = self.config.get("runware_models", [])
        self.logger.debug(f"Retrieved {len(models)} Runware AI models from config")

        return models

    def set_runware_models(self, models):
        """Set the list of Runware AI models in the configuration.

        Args:
            models (list): List of Runware AI models with id and name.

        Returns:
            bool: True if successful, False otherwise.
        """
        self.logger.debug(f"Setting {len(models)} Runware AI models in config")

        # Store the models
        self.config["runware_models"] = models

        # Save the config
        self._save_config()

        # Verify the models were saved correctly
        saved_models = self.get_runware_models()
        if len(saved_models) != len(models):
            self.logger.error(f"Failed to save Runware AI models. Expected: {len(models)}, Got: {len(saved_models)}")
            return False

        self.logger.debug(f"Runware AI models saved successfully")
        return True
    def get_api_providers(self):
        """Get the list of API providers from the configuration.

        Returns:
            dict: Dictionary of API providers with their models.
        """
        # Reload the config from disk to ensure we have the latest version
        try:
            with open(self.config_path, 'r') as f:
                self.config = json.load(f)
                self.logger.debug(f"Reloaded config from {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error reloading config: {e}")

        # Get the providers - try new format first, then fall back to old format
        providers = self.config.get("api_providers", {})

        if not providers:
            # Fall back to old runware_models format for backward compatibility
            runware_models = self.config.get("runware_models", [])
            if runware_models:
                providers = {
                    "together_ai": {
                        "name": "Together AI",
                        "models": [
                            {
                                "id": "black-forest-labs/FLUX.1-schnell-Free",
                                "name": "FLUX.1-schnell-Free",
                                "default_steps": 4,
                                "max_steps": 4
                            }
                        ]
                    },
                    "runware_ai": {
                        "name": "Runware AI",
                        "models": [
                            {
                                "id": model["id"],
                                "name": model["name"],
                                "default_steps": 4 if "schnell" in model["name"].lower() else 28,
                                "max_steps": 50
                            }
                            for model in runware_models
                        ]
                    }
                }
                self.logger.debug("Converted old runware_models format to new api_providers format")

        self.logger.debug(f"Retrieved {len(providers)} API providers from config")
        return providers

    def get_provider_models(self, provider_key):
        """Get the list of models for a specific provider.

        Args:
            provider_key (str): Provider key (e.g., 'together_ai', 'runware_ai').

        Returns:
            list: List of models for the provider.
        """
        providers = self.get_api_providers()
        provider = providers.get(provider_key, {})
        models = provider.get("models", [])
        self.logger.debug(f"Retrieved {len(models)} models for provider {provider_key}")
        return models


    def get_api_providers(self):
        """Get the list of API providers from the configuration."""
        try:
            with open(self.config_path, "r") as f:
                self.config = json.load(f)
        except Exception as e:
            self.logger.error(f"Error reloading config: {e}")

        providers = self.config.get("api_providers", {})

        if not providers:
            runware_models = self.config.get("runware_models", [])
            if runware_models:
                providers = {
                    "together_ai": {
                        "name": "Together AI",
                        "models": [
                            {
                                "id": "black-forest-labs/FLUX.1-schnell-Free",
                                "name": "FLUX.1-schnell-Free",
                                "default_steps": 4,
                                "max_steps": 4
                            }
                        ]
                    },
                    "runware_ai": {
                        "name": "Runware AI",
                        "models": [
                            {
                                "id": model["id"],
                                "name": model["name"],
                                "default_steps": 4 if "schnell" in model["name"].lower() else 28,
                                "max_steps": 50
                            }
                            for model in runware_models
                        ]
                    }
                }

        return providers

    def get_provider_models(self, provider_key):
        """Get the list of models for a specific provider."""
        providers = self.get_api_providers()
        provider = providers.get(provider_key, {})
        models = provider.get("models", [])
        return models

